/**
 * Principal Routes
 * Handles all routes for the Principal dashboard and functionality
 */

const express = require('express');
const router = express.Router();
const principalController = require('../controllers/principal-controller');
const { checkAuthenticated } = require('../middleware/auth');

// Middleware to check if user is principal or admin
const checkPrincipal = (req, res, next) => {
  if (req.session.userRole === 'admin' || req.session.userRole === 'principal') {
    return next();
  }

  res.status(403).render('error', {
    title: 'Access Denied',
    message: 'You do not have permission to access the Principal dashboard. This feature requires principal or admin privileges.',
    error: { status: 403 },
    layout: 'layouts/main'
  });
};

// Apply authentication middleware to all routes
router.use(checkAuthenticated);
router.use(checkPrincipal);

// Middleware to ensure principal layout is used
router.use((req, res, next) => {
  res.locals.layout = 'layouts/principal';
  next();
});

// Dashboard
router.get('/dashboard', principalController.getDashboard);
router.get('/', principalController.getDashboard);

// Academic Progress
router.get('/academic-progress', principalController.getAcademicProgress);

// Teacher Management
router.get('/teacher-management', principalController.getTeacherManagement);
router.get('/teacher-timetables', principalController.getTeacherTimetables);
router.get('/teacher/:id', principalController.getTeacherDetails);

// Enhanced teacher profile API (accessible to principal)
const teacherProfileEnhancedApi = require('./api/teacher-profile-enhanced-api');
router.use('/api/teacher', teacherProfileEnhancedApi);

// PDF Generation APIs
const PDFDocument = require('pdfkit');
const path = require('path');
const fs = require('fs');

// Test PDF generation endpoint
router.post('/api/generate-test-pdf', async (req, res) => {
  try {
    console.log('🔄 Generating test PDF...');
    const { data } = req.body;

    // Generate unique filename
    const timestamp = Date.now();
    const filename = `test_pdf_${timestamp}.pdf`;
    const outputDir = path.join(__dirname, '../public/uploads/pdf');
    const outputPath = path.join(outputDir, filename);

    // Ensure directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Create PDF document
    const doc = new PDFDocument({ margin: 50 });
    const writeStream = fs.createWriteStream(outputPath);
    doc.pipe(writeStream);

    // Add content
    doc.fontSize(20).text('PDF Generation Test', { align: 'center' });
    doc.moveDown();
    doc.fontSize(14).text(`Generated at: ${data.generatedAt}`);
    doc.moveDown();
    doc.fontSize(12).text(data.content);
    doc.moveDown();
    doc.text(`System: ${data.system}`);
    doc.moveDown(2);
    doc.fontSize(10).text('This PDF was generated using PDFKit on the server-side, following the same pattern as other PDF generation in the application.');

    // Finalize PDF
    doc.end();

    // Wait for PDF to be written
    await new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });

    // Return public URL
    const publicUrl = `/uploads/pdf/${filename}`;
    console.log('✅ Test PDF generated:', publicUrl);

    res.json({ success: true, url: publicUrl });
  } catch (error) {
    console.error('❌ Error generating test PDF:', error);
    res.status(500).json({ success: false, message: 'Failed to generate test PDF' });
  }
});

// Teacher CV PDF generation endpoint
router.post('/api/generate-teacher-cv-pdf', async (req, res) => {
  try {
    console.log('🔄 Generating teacher CV PDF...');
    const { teacherId, teacherData } = req.body;

    // Generate unique filename
    const timestamp = Date.now();
    const teacherName = (teacherData.name || 'Teacher').replace(/[^a-zA-Z0-9]/g, '_');
    const filename = `${teacherName}_CV_${timestamp}.pdf`;
    const outputDir = path.join(__dirname, '../public/uploads/pdf');
    const outputPath = path.join(outputDir, filename);

    // Ensure directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Create PDF document
    const doc = new PDFDocument({ margin: 50 });
    const writeStream = fs.createWriteStream(outputPath);
    doc.pipe(writeStream);

    // Add header
    doc.fontSize(24).text('CURRICULUM VITAE', { align: 'center' });
    doc.moveDown();
    doc.fontSize(18).text(teacherData.name || 'Teacher Name', { align: 'center' });
    doc.fontSize(14).text(teacherData.designation || 'Teacher', { align: 'center' });
    doc.fontSize(12).text(teacherData.department || 'Academic Department', { align: 'center' });
    doc.moveDown(2);

    // Personal Information Section
    doc.fontSize(16).text('PERSONAL INFORMATION', { underline: true });
    doc.moveDown();
    doc.fontSize(12);
    doc.text(`Employee ID: ${teacherData.employee_id || 'N/A'}`);
    doc.text(`Email: ${teacherData.email || 'N/A'}`);
    doc.text(`Phone: ${teacherData.phone || 'N/A'}`);
    doc.text(`Date of Birth: ${teacherData.date_of_birth || 'N/A'}`);
    doc.text(`Gender: ${teacherData.gender || 'N/A'}`);
    doc.moveDown();

    // Professional Experience Section
    doc.fontSize(16).text('PROFESSIONAL EXPERIENCE', { underline: true });
    doc.moveDown();
    doc.fontSize(12);
    doc.text(`Total Experience: ${teacherData.total_experience_years || 0} years`);
    doc.text(`Teaching Experience: ${teacherData.teaching_experience_years || 0} years`);
    doc.text(`Subjects Taught: ${teacherData.subjects_taught || 'N/A'}`);
    doc.moveDown();

    // Administrative Details Section
    doc.fontSize(16).text('ADMINISTRATIVE DETAILS', { underline: true });
    doc.moveDown();
    doc.fontSize(12);
    doc.text(`Joining Date: ${teacherData.joining_date || 'N/A'}`);
    doc.text(`Employment Type: ${teacherData.employment_type || 'N/A'}`);
    doc.text(`Office Location: ${teacherData.office_location || 'N/A'}`);
    doc.text(`Performance Rating: ${teacherData.performance_rating || 'N/A'}`);
    doc.text(`Account Status: ${teacherData.account_status || 'N/A'}`);
    doc.moveDown();

    // Add achievements if available
    if (teacherData.totalAchievements && teacherData.totalAchievements > 0) {
      doc.fontSize(16).text('ACHIEVEMENTS', { underline: true });
      doc.moveDown();
      doc.fontSize(12);
      doc.text(`Total Achievements: ${teacherData.totalAchievements}`);
      doc.moveDown();
    }

    // Add certifications if available
    if (teacherData.totalCertifications && teacherData.totalCertifications > 0) {
      doc.fontSize(16).text('CERTIFICATIONS', { underline: true });
      doc.moveDown();
      doc.fontSize(12);
      doc.text(`Total Certifications: ${teacherData.totalCertifications}`);
      doc.moveDown();
    }

    // Footer
    doc.moveDown(2);
    doc.fontSize(10).text(`Generated on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, { align: 'center' });
    doc.text('Teacher Management System - Senior Secondary Residential School for Meritorious Students', { align: 'center' });

    // Finalize PDF
    doc.end();

    // Wait for PDF to be written
    await new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });

    // Return public URL
    const publicUrl = `/uploads/pdf/${filename}`;
    console.log('✅ Teacher CV PDF generated:', publicUrl);

    res.json({ success: true, url: publicUrl });
  } catch (error) {
    console.error('❌ Error generating teacher CV PDF:', error);
    res.status(500).json({ success: false, message: 'Failed to generate teacher CV PDF' });
  }
});

// Enhanced CV PDF generation endpoint using EJS template
router.post('/api/generate-enhanced-cv-pdf', async (req, res) => {
  try {
    console.log('🔄 Generating enhanced CV PDF...');
    const { teacherId, teacherData } = req.body;

    if (!teacherData) {
      return res.status(400).json({
        success: false,
        message: 'Teacher data is required'
      });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const teacherName = (teacherData.name || 'Teacher').replace(/[^a-zA-Z0-9]/g, '_');
    const filename = `${teacherName}_Enhanced_CV_${timestamp}.pdf`;
    const outputDir = path.join(__dirname, '../public/uploads/pdf');
    const outputPath = path.join(outputDir, filename);
    const templatePath = path.join(__dirname, '../views/cv/cv_template.ejs');

    // Ensure directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Use the PDF generator utility
    const { generatePDF } = require('../utils/pdf-generator');

    // Prepare data for template
    const templateData = {
      teacher: {
        ...teacherData,
        // Ensure required fields have defaults
        name: teacherData.name || 'Teacher Name',
        designation: teacherData.designation || 'Teacher',
        department: teacherData.department || 'Academic Department',
        employee_id: teacherData.employee_id || `EMP${String(teacherId || '0000').padStart(4, '0')}`,
        // Format dates properly
        date_of_birth: teacherData.date_of_birth ? new Date(teacherData.date_of_birth) : null,
        joining_date: teacherData.joining_date ? new Date(teacherData.joining_date) : null,
        confirmation_date: teacherData.confirmation_date ? new Date(teacherData.confirmation_date) : null,
        // Ensure arrays exist
        experienceTimeline: teacherData.experienceTimeline || [],
        educationTimeline: teacherData.educationTimeline || [],
        achievementsByCategory: teacherData.achievementsByCategory || {},
        skillsByCategory: teacherData.skillsByCategory || {}
      }
    };

    // Generate PDF using the template
    await generatePDF(templateData, templatePath, outputPath);

    // Return success response
    const pdfUrl = `/uploads/pdf/${filename}`;
    console.log('✅ Enhanced CV PDF generated successfully:', pdfUrl);

    res.json({
      success: true,
      message: 'Enhanced CV PDF generated successfully',
      url: pdfUrl,
      filename: filename
    });

  } catch (error) {
    console.error('❌ Error generating enhanced CV PDF:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating enhanced CV PDF',
      error: error.message
    });
  }
});

// Student Analytics
router.get('/student-analytics', principalController.getStudentAnalytics);

// Infrastructure Overview
router.get('/infrastructure', principalController.getInfrastructure);

// Profile
router.get('/profile', principalController.getProfile);

// Principal Profile API endpoint
router.get('/api/profile', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get comprehensive principal information from both users and staff tables
    const [users] = await db.query(`
      SELECT
        u.id, u.username, u.name, u.email, u.profile_image, u.bio,
        u.date_of_birth, u.gender, u.created_at, u.last_login, u.is_active,
        s.id as staff_id, s.employee_id, s.designation, s.department, s.joining_date, s.employment_type,
        s.phone, s.alternate_phone, s.emergency_contact, s.address, s.city, s.state, s.pincode,
        s.class_10_board, s.class_10_year, s.class_10_percentage, s.class_10_school,
        s.class_12_board, s.class_12_year, s.class_12_percentage, s.class_12_school, s.class_12_stream,
        s.graduation_degree, s.graduation_university, s.graduation_year, s.graduation_percentage, s.graduation_specialization,
        s.post_graduation_degree, s.post_graduation_university, s.post_graduation_year, s.post_graduation_percentage, s.post_graduation_specialization,
        s.phd_subject, s.phd_university, s.phd_year, s.phd_thesis_title,
        s.other_qualifications, s.professional_certifications,
        s.total_experience_years, s.teaching_experience_years, s.administrative_experience_years,
        s.previous_organizations, s.current_salary, s.subjects_taught, s.classes_handled,
        s.awards_received, s.publications, s.research_papers, s.conferences_attended, s.training_programs,
        s.special_skills, s.languages_known,
        s.office_location, s.reporting_manager_id, s.probation_period_months, s.confirmation_date,
        s.last_promotion_date, s.performance_rating
      FROM users u
      LEFT JOIN staff s ON u.id = s.user_id
      WHERE u.id = ? AND u.role = 'principal'
    `, [req.session.userId]);

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Principal profile not found'
      });
    }

    const principal = users[0];

    // Get educational qualifications if staff_id exists (with error handling)
    let educationTimeline = [];
    if (principal.staff_id) {
      try {
        const [educationResult] = await db.query(`
          SELECT
            eq.qualification_level,
            eq.qualification_name,
            eq.specialization,
            eq.institution_name,
            eq.university_board,
            eq.total_marks_obtained,
            eq.total_marks_maximum,
            eq.percentage,
            eq.grade,
            eq.cgpa,
            eq.completion_year,
            eq.subjects,
            eq.achievements,
            eq.thesis_title
          FROM staff_educational_qualifications eq
          WHERE eq.staff_id = ?
          ORDER BY eq.completion_year ASC
        `, [principal.staff_id]);
        educationTimeline = educationResult;
      } catch (eduError) {
        console.log('Educational qualifications table not found, using fallback data');
        educationTimeline = [];
      }
    }

    // Get professional experience if staff_id exists (with error handling)
    let experienceTimeline = [];
    if (principal.staff_id) {
      try {
        const [experienceResult] = await db.query(`
          SELECT
            pe.job_title,
            pe.department,
            pe.employment_type,
            pe.job_category,
            pe.organization_name,
            pe.organization_type,
            pe.location,
            pe.start_date,
            pe.end_date,
            pe.is_current,
            pe.job_description,
            pe.key_achievements,
            pe.salary_range,
            pe.reporting_to,
            pe.team_size_managed
          FROM staff_professional_experience pe
          WHERE pe.staff_id = ?
          ORDER BY pe.start_date DESC
        `, [principal.staff_id]);
        experienceTimeline = experienceResult;
      } catch (expError) {
        console.log('Professional experience table not found, using fallback data');
        experienceTimeline = [];
      }
    }

    // Enhanced principal profile response
    const enhancedPrincipal = {
      ...principal,
      educationTimeline,
      experienceTimeline,
      employee_id: principal.employee_id || `EMP${String(principal.id).padStart(4, '0')}`,
      designation: principal.designation || "Principal",
      department: principal.department || "Administration",
      phone: principal.phone || "Not provided",
      joining_date: principal.joining_date || null,
      employment_type: principal.employment_type || "permanent",
      special_skills: principal.special_skills || "Educational Leadership, Administration, Strategic Planning",
      languages_known: principal.languages_known || "English, Hindi"
    };

    res.json({
      success: true,
      principal: enhancedPrincipal
    });
  } catch (error) {
    console.error('Error fetching principal profile:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching principal profile',
      error: error.message
    });
  }
});

// Principal CV Generation API endpoint
router.post('/api/generate-cv-pdf', async (req, res) => {
  try {
    console.log('🔄 Generating principal CV PDF...');
    const { principalData } = req.body;

    // Generate unique filename
    const timestamp = Date.now();
    const filename = `principal_cv_${timestamp}.pdf`;
    const filePath = path.join(__dirname, '../public/temp', filename);

    // Ensure temp directory exists
    const tempDir = path.join(__dirname, '../public/temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Create PDF document
    const doc = new PDFDocument({
      size: 'A4',
      margins: { top: 50, bottom: 50, left: 50, right: 50 }
    });

    // Pipe to file
    doc.pipe(fs.createWriteStream(filePath));

    // Add content to PDF
    doc.fontSize(20).font('Helvetica-Bold').text('CURRICULUM VITAE', { align: 'center' });
    doc.moveDown(1);

    // Personal Information
    doc.fontSize(16).font('Helvetica-Bold').text('PERSONAL INFORMATION', { underline: true });
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Name: ${principalData.name || 'N/A'}`);
    doc.text(`Email: ${principalData.email || 'N/A'}`);
    doc.text(`Phone: ${principalData.phone || 'N/A'}`);
    doc.text(`Employee ID: ${principalData.employee_id || 'N/A'}`);
    doc.text(`Designation: ${principalData.designation || 'Principal'}`);
    doc.text(`Department: ${principalData.department || 'Administration'}`);
    if (principalData.joining_date) {
      doc.text(`Joining Date: ${new Date(principalData.joining_date).toLocaleDateString()}`);
    }
    doc.moveDown(1);

    // Educational Qualifications
    if (principalData.educationTimeline && principalData.educationTimeline.length > 0) {
      doc.fontSize(16).font('Helvetica-Bold').text('EDUCATIONAL QUALIFICATIONS', { underline: true });
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica');

      principalData.educationTimeline.forEach(edu => {
        doc.font('Helvetica-Bold').text(`${edu.qualification_name || 'N/A'}`);
        doc.font('Helvetica').text(`Institution: ${edu.institution_name || 'N/A'}`);
        if (edu.university_board) doc.text(`University/Board: ${edu.university_board}`);
        if (edu.specialization) doc.text(`Specialization: ${edu.specialization}`);
        if (edu.completion_year) doc.text(`Year: ${edu.completion_year}`);
        if (edu.percentage) doc.text(`Percentage: ${edu.percentage}%`);
        doc.moveDown(0.5);
      });
      doc.moveDown(0.5);
    }

    // Professional Experience
    if (principalData.experienceTimeline && principalData.experienceTimeline.length > 0) {
      doc.fontSize(16).font('Helvetica-Bold').text('PROFESSIONAL EXPERIENCE', { underline: true });
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica');

      principalData.experienceTimeline.forEach(exp => {
        doc.font('Helvetica-Bold').text(`${exp.job_title || 'N/A'}`);
        doc.font('Helvetica').text(`Organization: ${exp.organization_name || 'N/A'}`);
        if (exp.department) doc.text(`Department: ${exp.department}`);
        if (exp.location) doc.text(`Location: ${exp.location}`);
        const startDate = exp.start_date ? new Date(exp.start_date).toLocaleDateString() : 'N/A';
        const endDate = exp.is_current ? 'Present' : (exp.end_date ? new Date(exp.end_date).toLocaleDateString() : 'N/A');
        doc.text(`Duration: ${startDate} - ${endDate}`);
        if (exp.job_description) doc.text(`Description: ${exp.job_description}`);
        doc.moveDown(0.5);
      });
      doc.moveDown(0.5);
    }

    // Skills and Achievements
    if (principalData.special_skills) {
      doc.fontSize(16).font('Helvetica-Bold').text('SKILLS', { underline: true });
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica').text(principalData.special_skills);
      doc.moveDown(1);
    }

    if (principalData.awards_received) {
      doc.fontSize(16).font('Helvetica-Bold').text('AWARDS & ACHIEVEMENTS', { underline: true });
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica').text(principalData.awards_received);
      doc.moveDown(1);
    }

    // Additional Information
    if (principalData.languages_known) {
      doc.fontSize(16).font('Helvetica-Bold').text('LANGUAGES', { underline: true });
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica').text(principalData.languages_known);
      doc.moveDown(1);
    }

    // Finalize PDF
    doc.end();

    // Wait for PDF to be written
    doc.on('end', () => {
      console.log('✅ Principal CV PDF generated successfully');
      res.json({
        success: true,
        message: 'Principal CV PDF generated successfully',
        filename: filename,
        downloadUrl: `/temp/${filename}`
      });
    });

  } catch (error) {
    console.error('❌ Error generating principal CV PDF:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating principal CV PDF',
      error: error.message
    });
  }
});

// Reports and Analytics
router.get('/reports', principalController.getReports);
router.get('/reports/academic', principalController.getAcademicReports);
router.get('/reports/attendance', principalController.getAttendanceReports);
router.get('/reports/performance', principalController.getPerformanceReports);

// API endpoints for real-time data
router.get('/api/dashboard-stats', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get real-time statistics
    const [stats] = await db.query(`
      SELECT
        (SELECT COUNT(*) FROM users WHERE role = 'teacher' AND is_active = 1) as total_teachers,
        (SELECT COUNT(*) FROM users WHERE role = 'student' AND is_active = 1) as total_students,
        (SELECT COUNT(*) FROM classes) as total_classes,
        (SELECT COUNT(*) FROM subjects) as total_subjects
    `);

    // Get today's lecture statistics
    const [lectureStats] = await db.query(`
      SELECT
        COUNT(*) as total_lectures_today,
        SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_lectures,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_lectures
      FROM teacher_lectures
      WHERE date = CURDATE()
    `);

    // Get syllabus progress
    const [syllabusProgress] = await db.query(`
      SELECT
        AVG(CASE WHEN status = 'completed' THEN 100 ELSE 0 END) as avg_completion
      FROM teacher_lectures
    `);

    res.json({
      success: true,
      data: {
        ...stats[0],
        ...lectureStats[0],
        syllabus_completion: syllabusProgress[0]?.avg_completion || 0
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch statistics' });
  }
});

// API endpoint for academic progress data
router.get('/api/academic-progress', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get class-wise syllabus completion
    const [classProgress] = await db.query(`
      SELECT
        c.name as class_name,
        s.name as subject_name,
        COUNT(tl.id) as total_topics,
        SUM(CASE WHEN tl.status = 'completed' THEN 1 ELSE 0 END) as completed_topics,
        ROUND((SUM(CASE WHEN tl.status = 'completed' THEN 1 ELSE 0 END) / COUNT(tl.id)) * 100, 2) as completion_percentage
      FROM classes c
      LEFT JOIN teacher_lectures tl ON c.name = tl.class_name
      LEFT JOIN subjects s ON s.name = tl.subject_name
      GROUP BY c.name, s.name
      ORDER BY c.name, s.name
    `);

    res.json({
      success: true,
      data: classProgress
    });
  } catch (error) {
    console.error('Error fetching academic progress:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch academic progress' });
  }
});

// API endpoint for teacher performance data
router.get('/api/teacher-performance', async (req, res) => {
  try {
    const db = require('../config/database');

    // Get teacher performance metrics
    const [teacherPerformance] = await db.query(`
      SELECT
        u.name as teacher_name,
        u.email as teacher_email,
        COUNT(tl.id) as total_lectures,
        SUM(CASE WHEN tl.status = 'delivered' THEN 1 ELSE 0 END) as delivered_lectures,
        SUM(CASE WHEN tl.status = 'pending' AND tl.date < CURDATE() THEN 1 ELSE 0 END) as overdue_lectures,
        ROUND((SUM(CASE WHEN tl.status = 'delivered' THEN 1 ELSE 0 END) / COUNT(tl.id)) * 100, 2) as completion_rate
      FROM users u
      LEFT JOIN teacher_lectures tl ON u.id = tl.teacher_id
      WHERE u.role = 'teacher' AND u.is_active = 1
      GROUP BY u.id, u.name, u.email
      ORDER BY completion_rate DESC
    `);

    res.json({
      success: true,
      data: teacherPerformance
    });
  } catch (error) {
    console.error('Error fetching teacher performance:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch teacher performance' });
  }
});

// API endpoint for classroom details
router.get('/api/classroom/:roomId', async (req, res) => {
  try {
    const { roomId } = req.params;
    const db = require('../config/database');

    console.log('🔍 API: Fetching classroom details for roomId:', roomId);

    // Get detailed classroom information from rooms table
    // Try to find by ID first, then by room_number
    const [roomInfo] = await db.query(`
      SELECT
        id,
        room_number,
        floor,
        capacity,
        building
      FROM rooms
      WHERE id = ? OR room_number = ? OR room_number = ?
    `, [roomId, roomId, `Room ${roomId}`]);

    console.log('🔍 API: Room query result:', roomInfo);

    if (roomInfo.length === 0) {
      console.log('❌ API: Room not found for roomId:', roomId);
      return res.status(404).json({ success: false, error: 'Room not found' });
    }

    const classroom = roomInfo[0];

    // Get students assigned to this room through student_classrooms relationship
    // First try the students table, if it doesn't exist, use users table
    let students = [];
    try {
      const [studentsResult] = await db.query(`
        SELECT
          id,
          student_id,
          name,
          father_name,
          mother_name,
          gender,
          class,
          section,
          session,
          roll_no,
          contact_no,
          admission_no,
          dob,
          height,
          weight,
          caste_category_name,
          religion_name,
          medium_name,
          bpl,
          disability,
          cur_address,
          village_ward,
          pin_code
        FROM students
        WHERE (room_number = ? OR room_number = ?) AND is_active = 1
        ORDER BY class, section, roll_no, name
      `, [classroom.room_number, `Room ${roomId}`]);
      students = studentsResult;
      console.log('🔍 API: Students query result from students table:', students.length, 'students found');
    } catch (error) {
      console.log('🔍 API: Students table not found, trying student_classrooms approach...');

      // Try to get students through student_classrooms and classrooms relationship
      try {
        const [studentsResult] = await db.query(`
          SELECT DISTINCT
            u.id,
            u.username as student_id,
            u.name,
            u.full_name as father_name,
            '' as mother_name,
            'Male' as gender,
            cl.grade as class,
            cl.section,
            c.session,
            '' as roll_no,
            '' as contact_no,
            '' as admission_no,
            u.date_of_birth as dob,
            0 as height,
            0 as weight,
            '' as caste_category_name,
            '' as religion_name,
            '' as medium_name,
            'No' as bpl,
            'No' as disability,
            '' as cur_address,
            '' as village_ward,
            '' as pin_code
          FROM student_classrooms sc
          JOIN users u ON sc.student_id = u.id
          JOIN classrooms c ON sc.classroom_id = c.id
          JOIN classes cl ON c.class_id = cl.id
          JOIN rooms r ON c.room_id = r.id
          WHERE r.id = ? AND u.role = 'student' AND sc.status = 'active'
          ORDER BY cl.grade, cl.section, u.name
        `, [classroom.id]);
        students = studentsResult;
        console.log('🔍 API: Students query result from student_classrooms:', students.length, 'students found');
      } catch (error2) {
        console.log('🔍 API: No student_classrooms table either, trying users table directly...');

        // Final fallback: try to get students from users table directly
        try {
          const [studentsResult] = await db.query(`
            SELECT DISTINCT
              u.id,
              u.username as student_id,
              u.name,
              u.full_name as father_name,
              '' as mother_name,
              'Unknown' as gender,
              '' as class,
              '' as section,
              '' as session,
              '' as roll_no,
              '' as contact_no,
              '' as admission_no,
              u.date_of_birth as dob,
              0 as height,
              0 as weight,
              '' as caste_category_name,
              '' as religion_name,
              '' as medium_name,
              'No' as bpl,
              'No' as disability,
              '' as cur_address,
              '' as village_ward,
              '' as pin_code
            FROM users u
            WHERE u.role = 'student'
            ORDER BY u.name
            LIMIT 5
          `);
          students = studentsResult;
          console.log('🔍 API: Students query result from users table (sample):', students.length, 'students found');
        } catch (error3) {
          console.log('🔍 API: No student data available, using empty array');
          students = [];
        }
      }
    }

    // Get IT equipment for this room using foreign key relationship from inventory_items
    const [itEquipment] = await db.query(`
      SELECT
        i.name as item_name,
        CASE
          WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN 'projector'
          WHEN i.name LIKE '%UPS%' THEN 'other'
          WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN 'desktop'
          WHEN i.name LIKE '%Laptop%' THEN 'laptop'
          WHEN i.name LIKE '%PANEL%' THEN 'other'
          WHEN i.name LIKE '%CAMERA%' THEN 'other'
          WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN 'printer'
          WHEN i.name LIKE '%Router%' THEN 'network'
          ELSE 'other'
        END as item_type,
        i.serial_number,
        i.status,
        i.manufacturer,
        i.model,
        i.purchase_date,
        i.warranty_expiry,
        i.notes,
        'good' as condition_status,
        NULL as mac_address,
        NULL as ip_address,
        NULL as hostname,
        i.description,
        i.purchase_cost
      FROM inventory_items i
      WHERE i.room_id = ?
      ORDER BY
        CASE
          WHEN i.name LIKE '%projector%' OR i.name LIKE '%HITACHI%' OR i.name LIKE '%BENQ%' THEN 1
          WHEN i.name LIKE '%UPS%' THEN 2
          WHEN i.name LIKE '%PC%' OR i.name LIKE '%Desktop%' OR i.name LIKE '%VERITON%' THEN 3
          WHEN i.name LIKE '%Laptop%' THEN 4
          WHEN i.name LIKE '%Printer%' OR i.name LIKE '%HP LJ%' OR i.name LIKE '%MFP%' THEN 5
          ELSE 6
        END, i.name
    `, [classroom.id]);

    // Get electrical equipment for this room with detailed breakdown
    const [electricalEquipment] = await db.query(`
      SELECT
        item_name,
        item_type,
        serial_number,
        status,
        manufacturer,
        model,
        wattage,
        installation_date,
        notes
      FROM electrical_inventory
      WHERE room_number = ? OR room_number = ?
      ORDER BY item_type, item_name
    `, [classroom.room_number, `Room ${roomId}`]);

    console.log('🔍 API: Equipment query results:', {
      it: itEquipment.length,
      electrical: electricalEquipment.length
    });

    // Calculate gender breakdown from the new students table
    const boys = students.filter(s => s.gender === 'Male');
    const girls = students.filter(s => s.gender === 'Female');
    const others = students.filter(s => s.gender === 'Other');

    // Get class distribution
    const classDistribution = students.reduce((acc, student) => {
      const classKey = `${student.class}-${student.section || 'N/A'}`;
      if (!acc[classKey]) {
        acc[classKey] = { class: student.class, section: student.section, count: 0, students: [] };
      }
      acc[classKey].count++;
      acc[classKey].students.push(student);
      return acc;
    }, {});

    // Determine classroom assignment based on students
    let classroomName = 'Unassigned';
    if (students.length > 0) {
      const mostCommonClass = Object.values(classDistribution)
        .sort((a, b) => b.count - a.count)[0];
      if (mostCommonClass) {
        classroomName = `Class ${mostCommonClass.class}${mostCommonClass.section ? ` - ${mostCommonClass.section}` : ''}`;
      }
    }

    const responseData = {
      success: true,
      data: {
        classroom: {
          room_number: classroom.room_number,
          floor: classroom.floor,
          capacity: classroom.capacity,
          building: classroom.building,
          full_name: classroomName,
          incharge_name: null // Will be updated when teacher assignment is implemented
        },
        students: {
          total: students.length,
          boys: boys.length,
          girls: girls.length,
          others: others.length,
          list: students,
          boys_list: boys,
          girls_list: girls,
          others_list: others,
          class_distribution: Object.values(classDistribution)
        },
        equipment: {
          it: itEquipment,
          electrical: electricalEquipment,
          summary: {
            total_it: itEquipment.length,
            total_electrical: electricalEquipment.length,
            working_it: itEquipment.filter(item => item.status === 'working').length,
            working_electrical: electricalEquipment.filter(item => item.status === 'working').length,
            faulty_it: itEquipment.filter(item => item.status === 'faulty').length,
            faulty_electrical: electricalEquipment.filter(item => item.status === 'faulty').length
          }
        }
      }
    };

    console.log('✅ API: Sending response for room', roomId, ':', {
      room_number: classroom.room_number,
      students: students.length,
      it_equipment: itEquipment.length,
      electrical_equipment: electricalEquipment.length
    });

    res.json(responseData);
  } catch (error) {
    console.error('Error fetching classroom details:', error);

    // Return a more user-friendly response with basic room info
    res.json({
      success: true,
      data: {
        classroom: {
          room_number: `Room ${req.params.roomId}`,
          floor: 1,
          capacity: 50,
          building: null,
          full_name: 'Classroom Information',
          incharge_name: null
        },
        students: {
          total: 0,
          boys: 0,
          girls: 0,
          others: 0,
          list: [],
          boys_list: [],
          girls_list: [],
          others_list: [],
          class_distribution: []
        },
        equipment: {
          it: [],
          electrical: [],
          summary: {
            total_it: 0,
            total_electrical: 0,
            working_it: 0,
            working_electrical: 0,
            faulty_it: 0,
            faulty_electrical: 0
          }
        }
      },
      message: 'Classroom data partially available. Some features may be limited due to database configuration.'
    });
  }
});

// Student Data Management (Read-only for Principal)
router.get('/students', async (req, res) => {
    try {
        const db = require('../config/database');

        // Get pagination parameters
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 25;
        const offset = (page - 1) * limit;

        // Get filter parameters
        const filters = {
            class: req.query.class || '',
            section: req.query.section || '',
            session: req.query.session || '',
            gender: req.query.gender || '',
            stream: req.query.stream || '',
            bpl: req.query.bpl || '',
            disability: req.query.disability || '',
            search: req.query.search || ''
        };

        // Build WHERE clause
        let whereConditions = ['is_active = 1'];
        let queryParams = [];

        if (filters.class) {
            whereConditions.push('class = ?');
            queryParams.push(filters.class);
        }

        if (filters.section) {
            whereConditions.push('section = ?');
            queryParams.push(filters.section);
        }

        if (filters.session) {
            whereConditions.push('session = ?');
            queryParams.push(filters.session);
        }

        if (filters.gender) {
            whereConditions.push('gender = ?');
            queryParams.push(filters.gender);
        }

        if (filters.stream) {
            whereConditions.push('stream = ?');
            queryParams.push(filters.stream);
        }

        if (filters.bpl) {
            whereConditions.push('bpl = ?');
            queryParams.push(filters.bpl);
        }

        if (filters.disability) {
            whereConditions.push('disability = ?');
            queryParams.push(filters.disability);
        }

        if (filters.search) {
            whereConditions.push('(name LIKE ? OR student_id LIKE ? OR father_name LIKE ? OR roll_no LIKE ? OR admission_no LIKE ?)');
            const searchPattern = `%${filters.search}%`;
            queryParams.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get total count
        const [countResult] = await db.query(
            `SELECT COUNT(*) as total FROM students WHERE ${whereClause}`,
            queryParams
        );
        const totalStudents = countResult[0].total;
        const totalPages = Math.ceil(totalStudents / limit);

        // Get students with pagination
        const [students] = await db.query(
            `SELECT * FROM students
             WHERE ${whereClause}
             ORDER BY class, section, roll_no, name
             LIMIT ? OFFSET ?`,
            [...queryParams, limit, offset]
        );

        // Check if export is requested
        if (req.query.export === 'excel') {
            const XLSX = require('xlsx');

            // Prepare data for export
            const exportData = students.map(student => ({
                'S.No': student.sno || '',
                'Student ID': student.student_id || '',
                'UdiseCode': student.udise_code || '',
                'Name': student.name || '',
                'Father Name': student.father_name || '',
                'Mother Name': student.mother_name || '',
                'DOB': student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : '',
                'Gender': student.gender || '',
                'Class': student.class || '',
                'Section': student.section || '',
                'Stream': student.stream || '',
                'Trade': student.trade || '',
                'Caste Category': student.caste_category_name || '',
                'BPL': student.bpl || '',
                'Disability': student.disability || '',
                'Religion': student.religion_name || '',
                'Medium': student.medium_name || '',
                'Height': student.height || '',
                'Weight': student.weight || '',
                'Admission No': student.admission_no || '',
                'Admission Date': student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : '',
                'State': student.state_name || '',
                'District': student.district_name || '',
                'Address': student.cur_address || '',
                'Village/Ward': student.village_ward || '',
                'Gram Panchayat': student.gram_panchayat || '',
                'Pin Code': student.pin_code || '',
                'Roll No': student.roll_no || '',
                'Contact No': student.contact_no || '',
                'IFSC Code': student.ifsc_code || '',
                'Bank Name': student.bank_name || '',
                'Column1': student.column1 || '',
                'Account Holder Code': student.account_holder_code || '',
                'Account Holder': student.account_holder || '',
                'Account Holder Name': student.account_holder_name || ''
            }));

            // Create workbook and worksheet
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(exportData);

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Students');

            // Generate buffer
            const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

            // Set headers for download
            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="students_export_${new Date().toISOString().split('T')[0]}.xlsx"`);

            return res.send(buffer);
        }

        // Get filter options
        const [classes] = await db.query('SELECT DISTINCT class FROM students WHERE class IS NOT NULL AND class != "" ORDER BY class');
        const [sections] = await db.query('SELECT DISTINCT section FROM students WHERE section IS NOT NULL AND section != "" ORDER BY section');
        const [sessions] = await db.query('SELECT DISTINCT session FROM students WHERE session IS NOT NULL AND session != "" ORDER BY session');
        const [streams] = await db.query('SELECT DISTINCT stream FROM students WHERE stream IS NOT NULL AND stream != "" ORDER BY stream');

        res.render('principal/students', {
            title: 'Student Data Overview',
            pageTitle: 'Student Data Overview',
            layout: 'layouts/principal',
            currentPage: 'students',
            students,
            classes: classes.map(c => c.class),
            sections: sections.map(s => s.section),
            sessions: sessions.map(s => s.session),
            streams: streams.map(s => s.stream),
            pagination: {
                currentPage: page,
                totalPages,
                totalStudents,
                limit,
                hasNext: page < totalPages,
                hasPrev: page > 1
            },
            filters,
            flashSuccess: req.session.flashSuccess,
            flashError: req.session.flashError,
            flashInfo: req.session.flashInfo
        });

        // Clear flash messages
        delete req.session.flashSuccess;
        delete req.session.flashError;
        delete req.session.flashInfo;

    } catch (error) {
        console.error('Error loading student data page:', error);
        req.session.flashError = 'Error loading student data';
        res.redirect('/principal/dashboard');
    }
});

// Get trash data for modal (must come before /:id route)
router.get('/students/trash-data', async (req, res) => {
    try {
        const db = require('../config/database');

        // Get soft deleted students
        const [students] = await db.query(
            `SELECT
                id, student_id, name, class, section, contact_no, updated_at
             FROM students
             WHERE is_active = 0
             ORDER BY updated_at DESC
             LIMIT 50`
        );

        res.json({
            success: true,
            students: students
        });

    } catch (error) {
        console.error('Error fetching trash data:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching trash data'
        });
    }
});

// Get single student data for modal
router.get('/students/:id', async (req, res) => {
    try {
        const db = require('../config/database');
        const studentId = req.params.id;

        const [students] = await db.query(
            'SELECT * FROM students WHERE id = ? AND is_active = 1',
            [studentId]
        );

        if (students.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Student not found'
            });
        }

        res.json({
            success: true,
            student: students[0]
        });

    } catch (error) {
        console.error('Error fetching student:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching student data'
        });
    }
});

// Update single student (read-only for principal, but keeping for consistency)
router.put('/students/:id', async (req, res) => {
    try {
        const db = require('../config/database');
        const studentId = req.params.id;
        const updateData = req.body;

        // Remove empty values
        Object.keys(updateData).forEach(key => {
            if (updateData[key] === '' || updateData[key] === null) {
                delete updateData[key];
            }
        });

        if (Object.keys(updateData).length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No data to update'
            });
        }

        const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
        const updateValues = Object.values(updateData);
        updateValues.push(studentId);

        await db.query(
            `UPDATE students SET ${updateFields}, updated_at = NOW() WHERE id = ? AND is_active = 1`,
            updateValues
        );

        res.json({
            success: true,
            message: 'Student updated successfully'
        });

    } catch (error) {
        console.error('Error updating student:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating student'
        });
    }
});

// Export students data route
router.get('/students/export', async (req, res) => {
    try {
        const db = require('../config/database');

        // Get filter parameters (same as students route)
        const filters = {
            class: req.query.class || '',
            section: req.query.section || '',
            session: req.query.session || '',
            gender: req.query.gender || '',
            stream: req.query.stream || '',
            bpl: req.query.bpl || '',
            disability: req.query.disability || '',
            search: req.query.search || ''
        };

        // Build WHERE clause
        let whereConditions = ['s.is_active = 1'];
        let queryParams = [];

        if (filters.class) {
            whereConditions.push('s.class = ?');
            queryParams.push(filters.class);
        }
        if (filters.section) {
            whereConditions.push('s.section = ?');
            queryParams.push(filters.section);
        }
        if (filters.session) {
            whereConditions.push('s.session = ?');
            queryParams.push(filters.session);
        }
        if (filters.gender) {
            whereConditions.push('s.gender = ?');
            queryParams.push(filters.gender);
        }
        if (filters.stream) {
            whereConditions.push('s.stream = ?');
            queryParams.push(filters.stream);
        }
        if (filters.bpl) {
            whereConditions.push('s.bpl = ?');
            queryParams.push(filters.bpl);
        }
        if (filters.disability) {
            whereConditions.push('s.disability = ?');
            queryParams.push(filters.disability);
        }
        if (filters.search) {
            whereConditions.push('(s.name LIKE ? OR s.student_id LIKE ? OR s.father_name LIKE ? OR s.roll_no LIKE ?)');
            const searchTerm = `%${filters.search}%`;
            queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
        }

        const whereClause = whereConditions.join(' AND ');

        // Get all students matching filters (no pagination for export)
        const [students] = await db.query(
            `SELECT
                s.id, s.student_id, s.udise_code, s.name, s.father_name, s.mother_name,
                s.dob, s.gender, s.class, s.section, s.stream, s.trade, s.caste_category_name,
                s.bpl, s.disability, s.religion_name, s.medium_name, s.height, s.weight,
                s.admission_no, s.admission_date, s.state_name, s.district_name, s.cur_address,
                s.village_ward, s.gram_panchayat, s.pin_code, s.roll_no, s.contact_no,
                s.ifsc_code, s.bank_name, s.account_holder, s.account_holder_name,
                s.account_holder_code, s.session, s.created_at, s.updated_at
             FROM students s
             WHERE ${whereClause}
             ORDER BY s.class, s.section, s.roll_no, s.name`,
            queryParams
        );

        // Prepare CSV content
        const headers = [
            'S.No', 'Student ID', 'UdiseCode', 'Name', 'Father Name', 'Mother Name', 'DOB', 'Gender',
            'Class', 'Section', 'Stream', 'Trade', 'Caste Category', 'BPL', 'Disability',
            'Religion', 'Medium', 'Height', 'Weight', 'Admission No', 'Admission Date',
            'State', 'District', 'Address', 'Village/Ward', 'Gram Panchayat', 'Pin Code',
            'Roll No', 'Contact No', 'IFSC Code', 'Bank Name', 'Account Holder',
            'Account Holder Name', 'Account Holder Code', 'Session'
        ];

        let csvContent = headers.join(',') + '\n';

        students.forEach((student, index) => {
            const row = [
                index + 1,
                student.student_id || '',
                student.udise_code || '',
                student.name || '',
                student.father_name || '',
                student.mother_name || '',
                student.dob ? new Date(student.dob).toLocaleDateString('en-IN') : '',
                student.gender || '',
                student.class || '',
                student.section || '',
                student.stream || '',
                student.trade || '',
                student.caste_category_name || '',
                student.bpl || '',
                student.disability || '',
                student.religion_name || '',
                student.medium_name || '',
                student.height || '',
                student.weight || '',
                student.admission_no || '',
                student.admission_date ? new Date(student.admission_date).toLocaleDateString('en-IN') : '',
                student.state_name || '',
                student.district_name || '',
                student.cur_address || '',
                student.village_ward || '',
                student.gram_panchayat || '',
                student.pin_code || '',
                student.roll_no || '',
                student.contact_no || '',
                student.ifsc_code || '',
                student.bank_name || '',
                student.account_holder || '',
                student.account_holder_name || '',
                student.account_holder_code || '',
                student.session || ''
            ];

            // Escape commas and quotes in data
            const escapedRow = row.map(field => {
                const fieldStr = String(field);
                if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
                    return '"' + fieldStr.replace(/"/g, '""') + '"';
                }
                return fieldStr;
            });

            csvContent += escapedRow.join(',') + '\n';
        });

        // Set headers for CSV download
        const timestamp = new Date().toISOString().split('T')[0];
        const filename = `students_export_${timestamp}.csv`;

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.send(csvContent);

    } catch (error) {
        console.error('Error exporting students:', error);
        res.status(500).json({
            success: false,
            message: 'Error exporting student data'
        });
    }
});

module.exports = router;
